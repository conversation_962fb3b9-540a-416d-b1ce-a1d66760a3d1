{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__1daf8b13._.js", "server/edge/chunks/edge-wrapper_0cc0fab8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\.[\\w]+$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "a+lIWRKRxvnVQaEcqhJqHOiJoD1n5nRfxss0mxpJlgU=", "__NEXT_PREVIEW_MODE_ID": "dd5f677833a1b8001ffcd7152da27998", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "75070876f5706bda170caa732bf26f2e81a7245cfe1c7b191087e0590dd1a2e2", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "4be03910e3babbbe5f789b86267bb4bce5fdfe46cc15540cdc2c6042da94b2d9"}}}, "sortedMiddleware": ["/"], "functions": {}}