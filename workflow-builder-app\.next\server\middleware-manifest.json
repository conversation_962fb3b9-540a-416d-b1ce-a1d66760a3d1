{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_94a4b977._.js", "server/edge/chunks/[root of the server]__1daf8b13._.js", "server/edge/chunks/edge-wrapper_0cc0fab8.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next|_vercel|.*\\.[\\w]+$).*))(\\\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next|_vercel|.*\\.[\\w]+$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "a+lIWRKRxvnVQaEcqhJqHOiJoD1n5nRfxss0mxpJlgU=", "__NEXT_PREVIEW_MODE_ID": "2f4174e6f053d9912376986b4f27df7c", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "9313f1a6b625db07c7ab1c0f8abc8d0860b63a588b019fab10f980f93691ee8b", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "d268981712c94d424b66f2c1d9689794255f2b0580999b3e31b7723cb33cd287"}}}, "sortedMiddleware": ["/"], "functions": {}}