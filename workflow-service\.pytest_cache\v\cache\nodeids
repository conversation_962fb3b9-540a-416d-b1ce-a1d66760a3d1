["tests/components/ai/test_agentic_ai.py::test_agentic_ai_missing_api_key", "tests/components/ai/test_agentic_ai.py::test_agentic_ai_missing_autogen", "tests/components/ai/test_agentic_ai.py::test_agentic_ai_missing_objective", "tests/components/ai/test_agentic_ai.py::test_agentic_ai_successful_execution", "tests/test_conditional_node_fixes.py::TestConditionalNodeFixes::test_all_condition_input_handles_generated", "tests/test_conditional_node_fixes.py::TestConditionalNodeFixes::test_condition_2_input_handle_generation", "tests/test_conditional_node_fixes.py::TestConditionalNodeFixes::test_condition_count_logic", "tests/test_conditional_node_fixes.py::TestConditionalNodeFixes::test_constants_and_limits", "tests/test_conditional_node_fixes.py::TestConditionalNodeFixes::test_dynamic_outputs_generation", "tests/test_conditional_node_fixes.py::TestConditionalNodeFixes::test_input_handle_visibility_rules", "tests/test_conditional_node_fixes.py::TestConditionalNodeFixes::test_input_source_selection_logic", "tests/test_conditional_node_fixes.py::TestConditionalNodeFixes::test_input_visibility_rules", "tests/test_conditional_node_fixes.py::TestConditionalNodeFixes::test_num_additional_conditions_input", "tests/test_conditional_node_patterns.py::TestConditionalNodePatterns::test_constants_match_pattern", "tests/test_conditional_node_patterns.py::TestConditionalNodePatterns::test_follows_dynamic_input_pattern", "tests/test_conditional_node_patterns.py::TestConditionalNodePatterns::test_has_dynamic_condition_inputs", "tests/test_conditional_node_patterns.py::TestConditionalNodePatterns::test_has_dynamic_input_handles", "tests/test_conditional_node_patterns.py::TestConditionalNodePatterns::test_has_get_dynamic_outputs_method", "tests/test_conditional_node_patterns.py::TestConditionalNodePatterns::test_input_handle_visibility_rules", "tests/test_conditional_node_patterns.py::TestConditionalNodePatterns::test_no_hardcoded_frontend_logic", "tests/test_conditional_node_patterns.py::TestConditionalNodePatterns::test_primary_input_is_dual_purpose", "tests/test_conditional_node_patterns.py::TestConditionalNodePatterns::test_visibility_rules_follow_combine_text_pattern", "tests/test_conditional_node_schema_generation.py::TestConditionalNodeSchemaGeneration::test_conditional_node_excluded_from_nodes_array", "tests/test_conditional_node_schema_generation.py::TestConditionalNodeSchemaGeneration::test_conditional_node_not_processed_as_regular_node", "tests/test_conditional_node_schema_generation.py::TestConditionalNodeSchemaGeneration::test_conditional_routing_generated_in_transition", "tests/test_conditional_node_schema_generation.py::TestConditionalNodeSchemaGeneration::test_is_conditional_node_detection", "tests/test_conditional_node_schema_generation.py::TestConditionalNodeSchemaGeneration::test_multiple_conditional_nodes_excluded", "tests/test_conditional_node_schema_generation.py::TestConditionalNodeSchemaGeneration::test_no_separate_conditional_transitions_created", "tests/test_conditional_node_switch_case.py::TestConditionalNodeSwitchCase::test_evaluate_condition_equals_operator", "tests/test_conditional_node_switch_case.py::TestConditionalNodeSwitchCase::test_evaluate_condition_not_equals_operator", "tests/test_conditional_node_switch_case.py::TestConditionalNodeSwitchCase::test_successful_execute_method_single_condition", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_build_method_backward_compatibility", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_build_method_deprecation_warning", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_build_method_orientation_mapping", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_component_inputs", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_component_metadata", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_component_outputs", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_execute_auto_detect_columns", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_execute_auto_detect_records", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_execute_columns_orientation", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_execute_missing_input_data", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_execute_pandas_import_error", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_execute_records_orientation", "tests/test_data_to_dataframe_component.py::TestDataToDataFrameComponent::test_get_input_value", "tests/test_frontend_handle_simulation.py::TestFrontendHandleSimulation::test_scenario_1_initial_state", "tests/test_frontend_handle_simulation.py::TestFrontendHandleSimulation::test_scenario_2_adding_additional_conditions", "tests/test_frontend_handle_simulation.py::TestFrontendHandleSimulation::test_scenario_3_source_selection_single_change", "tests/test_frontend_handle_simulation.py::TestFrontendHandleSimulation::test_scenario_4_source_selection_multiple_changes", "tests/test_frontend_handle_simulation.py::TestFrontendHandleSimulation::test_scenario_5_change_back_to_node_output", "tests/test_frontend_handle_simulation.py::TestFrontendHandleSimulation::test_scenario_6_all_global_context", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_backward_compatibility_traditional_structures", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_exact_path_at_notation_deeply_nested", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_exact_path_at_notation_nested", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_exact_path_at_notation_simple", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_exact_path_at_notation_with_array_index", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_field_matching_mode_key_based_only", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_field_matching_mode_property_based_only", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_property_based_duplicate_property_names", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_property_based_missing_data_field", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_smart_search_mixed_structure_key_based_priority", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_smart_search_mixed_structure_property_based_fallback", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_smart_search_property_based_flat_structure", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_smart_search_property_based_multiple_fields", "tests/test_property_based_field_matching.py::TestPropertyBasedFieldMatching::test_smart_search_property_based_nested_structure", "tests/test_select_data_smart_search.py::TestSelectDataSmartSearch::test_exact_path_mode_unchanged", "tests/test_select_data_smart_search.py::TestSelectDataSmartSearch::test_legacy_build_method_with_smart_search", "tests/test_select_data_smart_search.py::TestSelectDataSmartSearch::test_smart_search_field_not_found", "tests/test_select_data_smart_search.py::TestSelectDataSmartSearch::test_smart_search_finds_nested_field", "tests/test_select_data_smart_search.py::TestSelectDataSmartSearch::test_smart_search_returns_first_match", "tests/test_select_data_smart_search.py::TestSelectDataSmartSearch::test_smart_search_with_array_in_structure", "tests/test_select_data_smart_search.py::TestSelectDataSmartSearch::test_smart_search_with_simple_structure", "tests/test_switch_router_handle_behavior.py::TestSwitchRouterHandleBehavior::test_adding_additional_conditions", "tests/test_switch_router_handle_behavior.py::TestSwitchRouterHandleBehavior::test_dynamic_condition_count_changes", "tests/test_switch_router_handle_behavior.py::TestSwitchRouterHandleBehavior::test_edge_case_all_global_context", "tests/test_switch_router_handle_behavior.py::TestSwitchRouterHandleBehavior::test_initial_state_default_configuration", "tests/test_switch_router_handle_behavior.py::TestSwitchRouterHandleBehavior::test_maximum_conditions", "tests/test_switch_router_handle_behavior.py::TestSwitchRouterHandleBehavior::test_source_selection_behavior_change_back", "tests/test_switch_router_handle_behavior.py::TestSwitchRouterHandleBehavior::test_source_selection_behavior_multiple_changes", "tests/test_switch_router_handle_behavior.py::TestSwitchRouterHandleBehavior::test_source_selection_behavior_single_change"]