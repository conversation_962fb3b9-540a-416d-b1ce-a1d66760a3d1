2025-05-29 12:49:55 - ComponentSystem - INFO - [setup_logger:467] Logger ComponentSystem configured with log file: logs\2025-05-29\ComponentSystem_12-49-55.log
2025-05-29 12:49:55 - ComponentSystem - INFO - [get_component_manager:1418] Creating new global ComponentManager instance
2025-05-29 12:49:55 - ComponentSystem - INFO - [__init__:87] Initializing ComponentManager with Kafka bootstrap servers: **************:9092
2025-05-29 12:49:55 - ComponentSystem - INFO - [__init__:92] Kafka Consumer Topic: node-execution-request
2025-05-29 12:49:55 - ComponentSystem - INFO - [__init__:93] Kafka Results Topic: node_results
2025-05-29 12:49:55 - ComponentSystem - INFO - [__init__:94] Kafka Consumer Group ID: node_executor_service
2025-05-29 12:49:55 - ComponentSystem - INFO - [__init__:95] Kafka Producer Request Timeout: 60000ms
2025-05-29 12:49:55 - ComponentSystem - INFO - [__init__:98] Kafka Consumer Fetch Min Bytes: 100
2025-05-29 12:49:55 - ComponentSystem - INFO - [__init__:101] Kafka Consumer Fetch Max Wait: 500ms
2025-05-29 12:49:55 - ComponentSystem - INFO - [__init__:104] Kafka Consumer Session Timeout: 10000ms
2025-05-29 12:49:55 - ComponentSystem - INFO - [__init__:107] Kafka Consumer Heartbeat Interval: 3000ms
2025-05-29 12:49:55 - ComponentSystem - INFO - [__init__:110] Default Node Retries: 3
2025-05-29 12:49:55 - ComponentSystem - INFO - [__init__:128] Initializing ThreadPoolExecutor with 4 workers
2025-05-29 12:49:55 - ComponentSystem - INFO - [__init__:132] ThreadPoolExecutor initialized
2025-05-29 12:49:55 - ComponentSystem - INFO - [discover_components:141] Discovering components
2025-05-29 12:49:55 - ComponentSystem - INFO - [discover_components:142] Component registry before discovery: []
2025-05-29 12:49:55 - ComponentSystem - INFO - [discover_components:153] Discovered components: []
2025-05-29 12:49:55 - ComponentSystem - INFO - [discover_component_modules:1360] Discovering component modules
2025-05-29 12:49:55 - ComponentSystem - INFO - [discover_component_modules:1376] Found 16 potential component files: ['alter_metadata_component.py', 'api_component.py', 'combine_text_component.py', 'combine_text_component_new.py', 'convert_script_data_component.py', 'data_to_dataframe_component.py', 'doc_component.py', 'dynamic_combine_text_component.py', 'gmail_component.py', 'gmail_tracker_component.py', 'id_generator_component.py', 'merge_data_component.py', 'message_to_data_component.py', 'select_data_component.py', 'split_text_component.py', 'text_analysis_component.py']
2025-05-29 12:49:55 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.alter_metadata_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:63] Registering component: ApiRequestNode -> ApiComponent
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode']
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextComponent -> CombineTextComponent
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent']
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:63] Registering component: combine_text -> CombineTextComponent
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text']
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:63] Registering component: DocComponent -> DocComponent
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent']
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:63] Registering component: SelectDataComponent -> SelectDataExecutor
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent']
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:63] Registering component: SplitTextComponent -> SplitTextComponent
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent']
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:63] Registering component: text_analysis -> TextAnalysisComponent
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis']
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:63] Registering component: AlterMetadataComponent -> AlterMetadataComponent
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent']
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:63] Registering component: ConvertScriptDataComponent -> ConvertScriptDataComponent
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent']
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:63] Registering component: DataToDataFrameComponent -> DataToDataFrameComponent
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent']
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:63] Registering component: MessageToDataComponent -> MessageToDataExecutor
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent']
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.alter_metadata_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.api_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.api_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.combine_text_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.combine_text_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.combine_text_component_new
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextComponent -> CombineTextComponent
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent']
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.combine_text_component_new
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.convert_script_data_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.convert_script_data_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.data_to_dataframe_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.data_to_dataframe_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.doc_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.doc_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.dynamic_combine_text_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:63] Registering component: CombineTextExecutor -> CombineTextExecutor
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor']
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.dynamic_combine_text_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.gmail_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:63] Registering component: GmailComponent -> GmailComponent
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent']
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.gmail_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.gmail_tracker_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:63] Registering component: GmailTrackerComponent -> GmailTrackerComponent
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent']
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.gmail_tracker_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.id_generator_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:63] Registering component: IDGeneratorComponent -> IDGeneratorComponent
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent', 'IDGeneratorComponent']
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.id_generator_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.merge_data_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:63] Registering component: MergeDataComponent -> MergeDataExecutor
2025-05-29 12:49:56 - ComponentSystem - INFO - [decorator:65] Component registry now contains: ['ApiRequestNode', 'CombineTextComponent', 'combine_text', 'DocComponent', 'SelectDataComponent', 'SplitTextComponent', 'text_analysis', 'AlterMetadataComponent', 'ConvertScriptDataComponent', 'DataToDataFrameComponent', 'MessageToDataComponent', 'CombineTextExecutor', 'GmailComponent', 'GmailTrackerComponent', 'IDGeneratorComponent', 'MergeDataComponent']
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.merge_data_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.message_to_data_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.message_to_data_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.select_data_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.select_data_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.split_text_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.split_text_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1386] Importing component module: app.components.text_analysis_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1389] Successfully imported component module: app.components.text_analysis_component
2025-05-29 12:49:56 - ComponentSystem - INFO - [discover_component_modules:1396] Imported 16 component modules: ['app.components.alter_metadata_component', 'app.components.api_component', 'app.components.combine_text_component', 'app.components.combine_text_component_new', 'app.components.convert_script_data_component', 'app.components.data_to_dataframe_component', 'app.components.doc_component', 'app.components.dynamic_combine_text_component', 'app.components.gmail_component', 'app.components.gmail_tracker_component', 'app.components.id_generator_component', 'app.components.merge_data_component', 'app.components.message_to_data_component', 'app.components.select_data_component', 'app.components.split_text_component', 'app.components.text_analysis_component']
2025-05-29 12:49:56 - ComponentSystem - INFO - [start_component:330] Creating Kafka consumer for component ApiRequestNode with configuration:
2025-05-29 12:49:56 - ComponentSystem - INFO - [start_component:333]   Bootstrap Servers: **************:9092
2025-05-29 12:49:56 - ComponentSystem - INFO - [start_component:334]   Group ID: node_executor_service
2025-05-29 12:49:56 - ComponentSystem - INFO - [start_component:335]   Topic: node-execution-request
2025-05-29 12:49:56 - ComponentSystem - INFO - [start_component:336]   Auto Offset Reset: latest (starting from the latest offset)
2025-05-29 12:49:56 - ComponentSystem - INFO - [start_component:337]   Auto Commit: Disabled (using manual offset commits)
2025-05-29 12:49:56 - ComponentSystem - INFO - [start_component:338]   Fetch Min Bytes: 100
2025-05-29 12:49:56 - ComponentSystem - INFO - [start_component:339]   Fetch Max Wait: 500ms
2025-05-29 12:49:56 - ComponentSystem - INFO - [start_component:340]   Session Timeout: 10000ms
2025-05-29 12:49:56 - ComponentSystem - INFO - [start_component:343]   Heartbeat Interval: 3000ms
2025-05-29 12:49:56 - ComponentSystem - INFO - [start_component:347] Creating new Kafka consumer for component: ApiRequestNode on topic: node-execution-request with group_id: node_executor_service
2025-05-29 12:50:02 - ComponentSystem - INFO - [start_component:356] Kafka consumer started successfully for component: ApiRequestNode
2025-05-29 12:50:02 - ComponentSystem - INFO - [start_component:378] Started component: ApiRequestNode, listening on topic: node-execution-request
2025-05-29 12:50:02 - ComponentSystem - INFO - [_consume_messages:501] Consumer loop started for component: ApiRequestNode
2025-05-29 13:18:24 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=309, TaskID=ApiRequestNode-node-execution-request-0-309-1748504904.7037632
2025-05-29 13:18:24 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-309-1748504904.7037632, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://ruh-admin-api.rapidinnovation.dev/api/v1/auth/service/login",
    "method": "POST",
    "query_params": null,
    "headers": {
      "Content-Type": "application/json"
    },
    "body": {
      "service_name": "blog-generator",
      "api_key": "86Xk5ekNACh9ApaBfBgLieWaCzAnqPYC"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "69b5aca8-c577-4c48-89ef-0ccabcb1ad0b",
  "correlation_id": "3c8f79c5-a498-4b59-8ca4-f9bb0c619319"
}
2025-05-29 13:18:24 - ComponentSystem - INFO - [_process_message:713] [ReqID:69b5aca8-c577-4c48-89ef-0ccabcb1ad0b] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319] Executing tool ApiRequestNode for RequestID=69b5aca8-c577-4c48-89ef-0ccabcb1ad0b, TaskID=ApiRequestNode-node-execution-request-0-309-1748504904.7037632
2025-05-29 13:18:27 - ComponentSystem - INFO - [_process_message:717] [ReqID:69b5aca8-c577-4c48-89ef-0ccabcb1ad0b] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319] Tool ApiRequestNode executed successfully for RequestID=69b5aca8-c577-4c48-89ef-0ccabcb1ad0b, TaskID=ApiRequestNode-node-execution-request-0-309-1748504904.7037632
2025-05-29 13:18:27 - ComponentSystem - INFO - [_send_result:1005] [ReqID:69b5aca8-c577-4c48-89ef-0ccabcb1ad0b] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319] Preparing to send result for component ApiRequestNode, RequestID=69b5aca8-c577-4c48-89ef-0ccabcb1ad0b
2025-05-29 13:18:27 - ComponentSystem - INFO - [get_producer:244] [ReqID:69b5aca8-c577-4c48-89ef-0ccabcb1ad0b] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319] Creating Kafka producer for component ApiRequestNode with configuration:
2025-05-29 13:18:27 - ComponentSystem - INFO - [get_producer:247] [ReqID:69b5aca8-c577-4c48-89ef-0ccabcb1ad0b] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319]   Bootstrap Servers: **************:9092
2025-05-29 13:18:27 - ComponentSystem - INFO - [get_producer:248] [ReqID:69b5aca8-c577-4c48-89ef-0ccabcb1ad0b] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319]   Acks: all (ensuring message is written to all in-sync replicas)
2025-05-29 13:18:27 - ComponentSystem - INFO - [get_producer:252] [ReqID:69b5aca8-c577-4c48-89ef-0ccabcb1ad0b] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319]   Request Timeout: 60000ms
2025-05-29 13:18:27 - ComponentSystem - INFO - [get_producer:255] [ReqID:69b5aca8-c577-4c48-89ef-0ccabcb1ad0b] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319]   Idempotence: Enabled (ensuring exactly-once delivery semantics)
2025-05-29 13:18:27 - ComponentSystem - INFO - [get_producer:259] [ReqID:69b5aca8-c577-4c48-89ef-0ccabcb1ad0b] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319] Creating new Kafka producer for component: ApiRequestNode with servers: **************:9092
2025-05-29 13:18:29 - ComponentSystem - INFO - [get_producer:266] [ReqID:69b5aca8-c577-4c48-89ef-0ccabcb1ad0b] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319] Kafka producer started successfully for component: ApiRequestNode
2025-05-29 13:18:29 - ComponentSystem - INFO - [_send_result:1118] [ReqID:69b5aca8-c577-4c48-89ef-0ccabcb1ad0b] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319] Sending Kafka response: RequestID=69b5aca8-c577-4c48-89ef-0ccabcb1ad0b, Response={
  "request_id": "69b5aca8-c577-4c48-89ef-0ccabcb1ad0b",
  "component_type": "ApiRequestNode",
  "result": {
    "component_type": "ApiRequestNode",
    "request_id": "69b5aca8-c577-4c48-89ef-0ccabcb1ad0b",
    "status": "success",
    "response": {
      "result": {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIyOWUyZjVkYy1mZjM0LTQ5NWQtYjFmZS00YTQ0ZWZjMzAxNWIiLCJ0eXBlIjoic2VydmljZSIsInNlcnZpY2VfbmFtZSI6ImJsb2ctZ2VuZXJhdG9yIiwiZXhwIjoxNzQ4NTkxMzA3fQ.ousNybqd2CcIM7BXrQCuWHn_K6dFbQCgN3UoBzY1Dn4",
        "token_type": "bearer"
      },
      "status_code": 200,
      "response_headers": {
        "Date": "Thu, 29 May 2025 07:48:27 GMT",
        "Content-Type": "application/json",
        "Transfer-Encoding": "chunked",
        "Connection": "keep-alive",
        "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
        "Cf-Cache-Status": "DYNAMIC",
        "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=SYS6xwwcaQn129M0Q9OLOO4bH9DhtquR2fUJviZuQUOWBx%2BqfDBLyaP1rSACHRoRTghTZyO%2FJd55LLvqgkfKxDxk3F3jBJabdT617xH5Q1WQZGU%2FWFus6P%2FX5%2BMFIjJ2RsvzV1l8%2F63MVBhh6w%3D%3D\"}]}",
        "Content-Encoding": "gzip",
        "Server": "cloudflare",
        "CF-RAY": "947463aa8da9fb6d-MRS",
        "alt-svc": "h3=\":443\"; ma=86400"
      },
      "error": null,
      "url": "https://ruh-admin-api.rapidinnovation.dev/api/v1/auth/service/login",
      "method": "POST"
    }
  },
  "status": "success",
  "timestamp": 1748504909.4151561
}
2025-05-29 13:18:29 - ComponentSystem - INFO - [_send_result:1127] [ReqID:69b5aca8-c577-4c48-89ef-0ccabcb1ad0b] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319] Sent result for component ApiRequestNode to topic node_results for RequestID=69b5aca8-c577-4c48-89ef-0ccabcb1ad0b
2025-05-29 13:18:29 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:69b5aca8-c577-4c48-89ef-0ccabcb1ad0b] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319] Successfully committed offset 310 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-309-1748504904.7037632
2025-05-29 13:18:29 - ComponentSystem - INFO - [_process_message:936] [ReqID:69b5aca8-c577-4c48-89ef-0ccabcb1ad0b] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=309, TaskID=ApiRequestNode-node-execution-request-0-309-1748504904.7037632
2025-05-29 13:18:45 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=310, TaskID=ApiRequestNode-node-execution-request-0-310-1748504925.0522583
2025-05-29 13:18:45 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-310-1748504925.0522583, Payload={
  "tool_name": "SelectDataComponent",
  "tool_parameters": {
    "input_data": {
      "result": {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIyOWUyZjVkYy1mZjM0LTQ5NWQtYjFmZS00YTQ0ZWZjMzAxNWIiLCJ0eXBlIjoic2VydmljZSIsInNlcnZpY2VfbmFtZSI6ImJsb2ctZ2VuZXJhdG9yIiwiZXhwIjoxNzQ4NTkxMzA3fQ.ousNybqd2CcIM7BXrQCuWHn_K6dFbQCgN3UoBzY1Dn4"
      }
    },
    "data_type": "Auto-Detect",
    "search_mode": "Smart Search",
    "field_matching_mode": "Auto-detect",
    "selector": "access_token"
  },
  "request_id": "a6505506-4ac5-433f-8ddb-ad7f76dd34b4",
  "correlation_id": "3c8f79c5-a498-4b59-8ca4-f9bb0c619319"
}
2025-05-29 13:18:45 - ComponentSystem - INFO - [_process_message:713] [ReqID:a6505506-4ac5-433f-8ddb-ad7f76dd34b4] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319] Executing tool SelectDataComponent for RequestID=a6505506-4ac5-433f-8ddb-ad7f76dd34b4, TaskID=ApiRequestNode-node-execution-request-0-310-1748504925.0522583
2025-05-29 13:18:45 - ComponentSystem - INFO - [_process_message:717] [ReqID:a6505506-4ac5-433f-8ddb-ad7f76dd34b4] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319] Tool SelectDataComponent executed successfully for RequestID=a6505506-4ac5-433f-8ddb-ad7f76dd34b4, TaskID=ApiRequestNode-node-execution-request-0-310-1748504925.0522583
2025-05-29 13:18:45 - ComponentSystem - INFO - [_send_result:1005] [ReqID:a6505506-4ac5-433f-8ddb-ad7f76dd34b4] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319] Preparing to send result for component ApiRequestNode, RequestID=a6505506-4ac5-433f-8ddb-ad7f76dd34b4
2025-05-29 13:18:45 - ComponentSystem - INFO - [_send_result:1118] [ReqID:a6505506-4ac5-433f-8ddb-ad7f76dd34b4] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319] Sending Kafka response: RequestID=a6505506-4ac5-433f-8ddb-ad7f76dd34b4, Response={
  "request_id": "a6505506-4ac5-433f-8ddb-ad7f76dd34b4",
  "component_type": "ApiRequestNode",
  "result": {
    "request_id": "a6505506-4ac5-433f-8ddb-ad7f76dd34b4",
    "status": "success",
    "result": {
      "output_data": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIyOWUyZjVkYy1mZjM0LTQ5NWQtYjFmZS00YTQ0ZWZjMzAxNWIiLCJ0eXBlIjoic2VydmljZSIsInNlcnZpY2VfbmFtZSI6ImJsb2ctZ2VuZXJhdG9yIiwiZXhwIjoxNzQ4NTkxMzA3fQ.ousNybqd2CcIM7BXrQCuWHn_K6dFbQCgN3UoBzY1Dn4"
    }
  },
  "status": "success",
  "timestamp": 1748504925.065616
}
2025-05-29 13:18:45 - ComponentSystem - INFO - [_send_result:1127] [ReqID:a6505506-4ac5-433f-8ddb-ad7f76dd34b4] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319] Sent result for component ApiRequestNode to topic node_results for RequestID=a6505506-4ac5-433f-8ddb-ad7f76dd34b4
2025-05-29 13:18:45 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:a6505506-4ac5-433f-8ddb-ad7f76dd34b4] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319] Successfully committed offset 311 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-310-1748504925.0522583
2025-05-29 13:18:45 - ComponentSystem - INFO - [_process_message:936] [ReqID:a6505506-4ac5-433f-8ddb-ad7f76dd34b4] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=310, TaskID=ApiRequestNode-node-execution-request-0-310-1748504925.0522583
2025-05-29 13:18:58 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=311, TaskID=ApiRequestNode-node-execution-request-0-311-1748504938.6153479
2025-05-29 13:18:58 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-311-1748504938.6153479, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": "Authorization : \"Bearer  ",
    "num_additional_inputs": 2,
    "separator": null,
    "input_1": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIyOWUyZjVkYy1mZjM0LTQ5NWQtYjFmZS00YTQ0ZWZjMzAxNWIiLCJ0eXBlIjoic2VydmljZSIsInNlcnZpY2VfbmFtZSI6ImJsb2ctZ2VuZXJhdG9yIiwiZXhwIjoxNzQ4NTkxMzA3fQ.ousNybqd2CcIM7BXrQCuWHn_K6dFbQCgN3UoBzY1Dn4",
    "input_2": "\"}",
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "59a40b52-e543-43dc-8d86-99a668126666",
  "correlation_id": "3c8f79c5-a498-4b59-8ca4-f9bb0c619319"
}
2025-05-29 13:18:58 - ComponentSystem - INFO - [_process_message:713] [ReqID:59a40b52-e543-43dc-8d86-99a668126666] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319] Executing tool CombineTextComponent for RequestID=59a40b52-e543-43dc-8d86-99a668126666, TaskID=ApiRequestNode-node-execution-request-0-311-1748504938.6153479
2025-05-29 13:18:58 - ComponentSystem - INFO - [_process_message:717] [ReqID:59a40b52-e543-43dc-8d86-99a668126666] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319] Tool CombineTextComponent executed successfully for RequestID=59a40b52-e543-43dc-8d86-99a668126666, TaskID=ApiRequestNode-node-execution-request-0-311-1748504938.6153479
2025-05-29 13:18:58 - ComponentSystem - INFO - [_send_result:1005] [ReqID:59a40b52-e543-43dc-8d86-99a668126666] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319] Preparing to send result for component ApiRequestNode, RequestID=59a40b52-e543-43dc-8d86-99a668126666
2025-05-29 13:18:58 - ComponentSystem - INFO - [_send_result:1030] [ReqID:59a40b52-e543-43dc-8d86-99a668126666] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319] Result contains error status or error field for RequestID=59a40b52-e543-43dc-8d86-99a668126666
2025-05-29 13:18:58 - ComponentSystem - INFO - [_send_result:1118] [ReqID:59a40b52-e543-43dc-8d86-99a668126666] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319] Sending Kafka response: RequestID=59a40b52-e543-43dc-8d86-99a668126666, Response={
  "request_id": "59a40b52-e543-43dc-8d86-99a668126666",
  "status": "error",
  "result": {
    "error": "Error combining text for request_id 59a40b52-e543-43dc-8d86-99a668126666: 'NoneType' object has no attribute 'replace'"
  }
}
2025-05-29 13:18:58 - ComponentSystem - INFO - [_send_result:1127] [ReqID:59a40b52-e543-43dc-8d86-99a668126666] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319] Sent result for component ApiRequestNode to topic node_results for RequestID=59a40b52-e543-43dc-8d86-99a668126666
2025-05-29 13:18:59 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:59a40b52-e543-43dc-8d86-99a668126666] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319] Successfully committed offset 312 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-311-1748504938.6153479
2025-05-29 13:18:59 - ComponentSystem - INFO - [_process_message:936] [ReqID:59a40b52-e543-43dc-8d86-99a668126666] [CorrID:3c8f79c5-a498-4b59-8ca4-f9bb0c619319] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=311, TaskID=ApiRequestNode-node-execution-request-0-311-1748504938.6153479
2025-05-29 13:22:25 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=312, TaskID=ApiRequestNode-node-execution-request-0-312-1748505145.803559
2025-05-29 13:22:25 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-312-1748505145.803559, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://ruh-admin-api.rapidinnovation.dev/api/v1/auth/service/login",
    "method": "POST",
    "query_params": null,
    "headers": {
      "Content-Type": "application/json"
    },
    "body": {
      "service_name": "blog-generator",
      "api_key": "86Xk5ekNACh9ApaBfBgLieWaCzAnqPYC"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "0be41344-538b-4456-9337-5efa1e5ef29f",
  "correlation_id": "c2c33eb4-68b7-4953-99e5-ef05514dfea7"
}
2025-05-29 13:22:25 - ComponentSystem - INFO - [_process_message:713] [ReqID:0be41344-538b-4456-9337-5efa1e5ef29f] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Executing tool ApiRequestNode for RequestID=0be41344-538b-4456-9337-5efa1e5ef29f, TaskID=ApiRequestNode-node-execution-request-0-312-1748505145.803559
2025-05-29 13:22:28 - ComponentSystem - INFO - [_process_message:717] [ReqID:0be41344-538b-4456-9337-5efa1e5ef29f] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Tool ApiRequestNode executed successfully for RequestID=0be41344-538b-4456-9337-5efa1e5ef29f, TaskID=ApiRequestNode-node-execution-request-0-312-1748505145.803559
2025-05-29 13:22:28 - ComponentSystem - INFO - [_send_result:1005] [ReqID:0be41344-538b-4456-9337-5efa1e5ef29f] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Preparing to send result for component ApiRequestNode, RequestID=0be41344-538b-4456-9337-5efa1e5ef29f
2025-05-29 13:22:28 - ComponentSystem - INFO - [_send_result:1118] [ReqID:0be41344-538b-4456-9337-5efa1e5ef29f] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Sending Kafka response: RequestID=0be41344-538b-4456-9337-5efa1e5ef29f, Response={
  "request_id": "0be41344-538b-4456-9337-5efa1e5ef29f",
  "component_type": "ApiRequestNode",
  "result": {
    "component_type": "ApiRequestNode",
    "request_id": "0be41344-538b-4456-9337-5efa1e5ef29f",
    "status": "success",
    "response": {
      "result": {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIyOWUyZjVkYy1mZjM0LTQ5NWQtYjFmZS00YTQ0ZWZjMzAxNWIiLCJ0eXBlIjoic2VydmljZSIsInNlcnZpY2VfbmFtZSI6ImJsb2ctZ2VuZXJhdG9yIiwiZXhwIjoxNzQ4NTkxNTQ3fQ.mxntPNo-F9z04eXbFED9lREnIqnU9qglXtyowlibAQs",
        "token_type": "bearer"
      },
      "status_code": 200,
      "response_headers": {
        "Date": "Thu, 29 May 2025 07:52:27 GMT",
        "Content-Type": "application/json",
        "Transfer-Encoding": "chunked",
        "Connection": "keep-alive",
        "Nel": "{\"report_to\":\"cf-nel\",\"success_fraction\":0.0,\"max_age\":604800}",
        "Cf-Cache-Status": "DYNAMIC",
        "Report-To": "{\"group\":\"cf-nel\",\"max_age\":604800,\"endpoints\":[{\"url\":\"https://a.nel.cloudflare.com/report/v4?s=Z%2BfBf1wMJ05WsSBHvqcE6JY0S5ug4VebTY9ePZ7j4JunEkuixg64trSQka%2BtKie383xBMqfySGLvKtb21M7QqPJfu%2BRcb7ZpqsnsBMfWq1Kf9H1hG9jgzndqUSwIgD6%2FGKQaUmENJ3Ludf8fMQ%3D%3D\"}]}",
        "Content-Encoding": "gzip",
        "Server": "cloudflare",
        "CF-RAY": "9474698aeaf0e220-MRS",
        "alt-svc": "h3=\":443\"; ma=86400"
      },
      "error": null,
      "url": "https://ruh-admin-api.rapidinnovation.dev/api/v1/auth/service/login",
      "method": "POST"
    }
  },
  "status": "success",
  "timestamp": 1748505148.177128
}
2025-05-29 13:22:28 - ComponentSystem - INFO - [_send_result:1127] [ReqID:0be41344-538b-4456-9337-5efa1e5ef29f] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Sent result for component ApiRequestNode to topic node_results for RequestID=0be41344-538b-4456-9337-5efa1e5ef29f
2025-05-29 13:22:28 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:0be41344-538b-4456-9337-5efa1e5ef29f] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Successfully committed offset 313 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-312-1748505145.803559
2025-05-29 13:22:28 - ComponentSystem - INFO - [_process_message:936] [ReqID:0be41344-538b-4456-9337-5efa1e5ef29f] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=312, TaskID=ApiRequestNode-node-execution-request-0-312-1748505145.803559
2025-05-29 13:22:38 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=313, TaskID=ApiRequestNode-node-execution-request-0-313-1748505158.7582953
2025-05-29 13:22:38 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-313-1748505158.7582953, Payload={
  "tool_name": "SelectDataComponent",
  "tool_parameters": {
    "input_data": {
      "result": {
        "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIyOWUyZjVkYy1mZjM0LTQ5NWQtYjFmZS00YTQ0ZWZjMzAxNWIiLCJ0eXBlIjoic2VydmljZSIsInNlcnZpY2VfbmFtZSI6ImJsb2ctZ2VuZXJhdG9yIiwiZXhwIjoxNzQ4NTkxNTQ3fQ.mxntPNo-F9z04eXbFED9lREnIqnU9qglXtyowlibAQs",
        "token_type": "bearer"
      }
    },
    "data_type": "Auto-Detect",
    "search_mode": "Smart Search",
    "field_matching_mode": "Auto-detect",
    "selector": "access_token"
  },
  "request_id": "83f92ad8-be09-4a26-b638-ab7c0c8ba496",
  "correlation_id": "c2c33eb4-68b7-4953-99e5-ef05514dfea7"
}
2025-05-29 13:22:38 - ComponentSystem - INFO - [_process_message:713] [ReqID:83f92ad8-be09-4a26-b638-ab7c0c8ba496] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Executing tool SelectDataComponent for RequestID=83f92ad8-be09-4a26-b638-ab7c0c8ba496, TaskID=ApiRequestNode-node-execution-request-0-313-1748505158.7582953
2025-05-29 13:22:38 - ComponentSystem - INFO - [_process_message:717] [ReqID:83f92ad8-be09-4a26-b638-ab7c0c8ba496] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Tool SelectDataComponent executed successfully for RequestID=83f92ad8-be09-4a26-b638-ab7c0c8ba496, TaskID=ApiRequestNode-node-execution-request-0-313-1748505158.7582953
2025-05-29 13:22:38 - ComponentSystem - INFO - [_send_result:1005] [ReqID:83f92ad8-be09-4a26-b638-ab7c0c8ba496] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Preparing to send result for component ApiRequestNode, RequestID=83f92ad8-be09-4a26-b638-ab7c0c8ba496
2025-05-29 13:22:38 - ComponentSystem - INFO - [_send_result:1118] [ReqID:83f92ad8-be09-4a26-b638-ab7c0c8ba496] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Sending Kafka response: RequestID=83f92ad8-be09-4a26-b638-ab7c0c8ba496, Response={
  "request_id": "83f92ad8-be09-4a26-b638-ab7c0c8ba496",
  "component_type": "ApiRequestNode",
  "result": {
    "request_id": "83f92ad8-be09-4a26-b638-ab7c0c8ba496",
    "status": "success",
    "result": {
      "output_data": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIyOWUyZjVkYy1mZjM0LTQ5NWQtYjFmZS00YTQ0ZWZjMzAxNWIiLCJ0eXBlIjoic2VydmljZSIsInNlcnZpY2VfbmFtZSI6ImJsb2ctZ2VuZXJhdG9yIiwiZXhwIjoxNzQ4NTkxNTQ3fQ.mxntPNo-F9z04eXbFED9lREnIqnU9qglXtyowlibAQs"
    }
  },
  "status": "success",
  "timestamp": 1748505158.7740488
}
2025-05-29 13:22:39 - ComponentSystem - INFO - [_send_result:1127] [ReqID:83f92ad8-be09-4a26-b638-ab7c0c8ba496] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Sent result for component ApiRequestNode to topic node_results for RequestID=83f92ad8-be09-4a26-b638-ab7c0c8ba496
2025-05-29 13:22:39 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:83f92ad8-be09-4a26-b638-ab7c0c8ba496] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Successfully committed offset 314 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-313-1748505158.7582953
2025-05-29 13:22:39 - ComponentSystem - INFO - [_process_message:936] [ReqID:83f92ad8-be09-4a26-b638-ab7c0c8ba496] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=313, TaskID=ApiRequestNode-node-execution-request-0-313-1748505158.7582953
2025-05-29 13:22:52 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=314, TaskID=ApiRequestNode-node-execution-request-0-314-1748505172.8796315
2025-05-29 13:22:52 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-314-1748505172.8796315, Payload={
  "tool_name": "CombineTextComponent",
  "tool_parameters": {
    "main_input": "Authorization : \"Bearer  ",
    "num_additional_inputs": 2,
    "separator": "_",
    "input_1": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIyOWUyZjVkYy1mZjM0LTQ5NWQtYjFmZS00YTQ0ZWZjMzAxNWIiLCJ0eXBlIjoic2VydmljZSIsInNlcnZpY2VfbmFtZSI6ImJsb2ctZ2VuZXJhdG9yIiwiZXhwIjoxNzQ4NTkxNTQ3fQ.mxntPNo-F9z04eXbFED9lREnIqnU9qglXtyowlibAQs",
    "input_2": "}",
    "input_3": null,
    "input_4": null,
    "input_5": null,
    "input_6": null,
    "input_7": null,
    "input_8": null,
    "input_9": null,
    "input_10": null
  },
  "request_id": "23473f7a-5da8-4cf9-8a67-7f4d6caffb54",
  "correlation_id": "c2c33eb4-68b7-4953-99e5-ef05514dfea7"
}
2025-05-29 13:22:52 - ComponentSystem - INFO - [_process_message:713] [ReqID:23473f7a-5da8-4cf9-8a67-7f4d6caffb54] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Executing tool CombineTextComponent for RequestID=23473f7a-5da8-4cf9-8a67-7f4d6caffb54, TaskID=ApiRequestNode-node-execution-request-0-314-1748505172.8796315
2025-05-29 13:22:52 - ComponentSystem - INFO - [_process_message:717] [ReqID:23473f7a-5da8-4cf9-8a67-7f4d6caffb54] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Tool CombineTextComponent executed successfully for RequestID=23473f7a-5da8-4cf9-8a67-7f4d6caffb54, TaskID=ApiRequestNode-node-execution-request-0-314-1748505172.8796315
2025-05-29 13:22:52 - ComponentSystem - INFO - [_send_result:1005] [ReqID:23473f7a-5da8-4cf9-8a67-7f4d6caffb54] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Preparing to send result for component ApiRequestNode, RequestID=23473f7a-5da8-4cf9-8a67-7f4d6caffb54
2025-05-29 13:22:52 - ComponentSystem - INFO - [_send_result:1118] [ReqID:23473f7a-5da8-4cf9-8a67-7f4d6caffb54] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Sending Kafka response: RequestID=23473f7a-5da8-4cf9-8a67-7f4d6caffb54, Response={
  "request_id": "23473f7a-5da8-4cf9-8a67-7f4d6caffb54",
  "component_type": "ApiRequestNode",
  "result": {
    "request_id": "23473f7a-5da8-4cf9-8a67-7f4d6caffb54",
    "status": "success",
    "result": {
      "status": "success",
      "result": "Authorization : \"Bearer  _eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIyOWUyZjVkYy1mZjM0LTQ5NWQtYjFmZS00YTQ0ZWZjMzAxNWIiLCJ0eXBlIjoic2VydmljZSIsInNlcnZpY2VfbmFtZSI6ImJsb2ctZ2VuZXJhdG9yIiwiZXhwIjoxNzQ4NTkxNTQ3fQ.mxntPNo-F9z04eXbFED9lREnIqnU9qglXtyowlibAQs_}"
    }
  },
  "status": "success",
  "timestamp": 1748505172.8960543
}
2025-05-29 13:22:53 - ComponentSystem - INFO - [_send_result:1127] [ReqID:23473f7a-5da8-4cf9-8a67-7f4d6caffb54] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Sent result for component ApiRequestNode to topic node_results for RequestID=23473f7a-5da8-4cf9-8a67-7f4d6caffb54
2025-05-29 13:22:53 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:23473f7a-5da8-4cf9-8a67-7f4d6caffb54] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Successfully committed offset 315 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-314-1748505172.8796315
2025-05-29 13:22:53 - ComponentSystem - INFO - [_process_message:936] [ReqID:23473f7a-5da8-4cf9-8a67-7f4d6caffb54] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=314, TaskID=ApiRequestNode-node-execution-request-0-314-1748505172.8796315
2025-05-29 13:23:11 - ComponentSystem - INFO - [_process_message:625] Starting message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=315, TaskID=ApiRequestNode-node-execution-request-0-315-1748505191.8518567
2025-05-29 13:23:11 - ComponentSystem - INFO - [_process_message:652] Received Kafka message: TaskID=ApiRequestNode-node-execution-request-0-315-1748505191.8518567, Payload={
  "tool_name": "ApiRequestNode",
  "tool_parameters": {
    "url": "https://ruh-admin-api.rapidinnovation.dev/api/v1/auth/service/login",
    "method": "POST",
    "query_params": null,
    "headers": {
      "Authorization": "Bearer _eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIyOWUyZjVkYy1mZjM0LTQ5NWQtYjFmZS00YTQ0ZWZjMzAxNWIiLCJ0eXBlIjoic2VydmljZSIsInNlcnZpY2VfbmFtZSI6ImJsb2ctZ2VuZXJhdG9yIiwiZXhwIjoxNzQ4NTkxNTQ3fQ.mxntPNo-F9z04eXbFED9lREnIqnU9qglXtyowlibAQs_"
    },
    "body": {
      "title": "Navigating AI Agent Workflows: Enhancing Operational Efficiency with Ruh.ai",
      "summary": "Discover how AI agent workflows revolutionize business processes by integrating adaptive learning and automation. Dive into the intricacies of workflow management and learn how Ruh.ai's solutions can effectively streamline operations, driving transformation across industries.",
      "category": "AI Innovation",
      "content": "<h1>The Evolution of AI Agent Workflows</h1><p>In the fast-paced world of technological advancement, AI agent workflows have emerged as a cornerstone for optimizing enterprise operations. These workflows synthesize automation, machine learning, and data analytics to deliver unprecedented efficiency.</p><h2>Understanding AI Agent Workflows</h2><p>AI agent workflows consist of a series of automated tasks executed by intelligent agents. These agents leverage cognitive computing and advanced algorithms to simulate human-like decision-making capabilities, epowering businesses to reduce manual intervention.</p><h3>Key Components:</h3><ul><li>Automation: Streamlining routine tasks for efficiency.</li><li>Machine Learning: Adapting algorithms for continuous improvement.</li><li>Data Analytics: Informing decisions through data-driven insights.</li></ul><h2>Transforming Operations with Ruh.ai</h2><p>Ruh.ai brings a bespoke approach to AI agent workflows, tailoring solutions to the unique requirements of different industries. By integrating cutting-edge AI technologies, Ruh.ai enables companies to achieve substantial operational transformation.</p><h3>Customized Solutions</h3><p>Ruh.ai develops customized AI workflows that align with specific business needs, ensuring seamless integration with existing systems and processes.</p><h3>Enhanced Decision-Making</h3><p>With Ruh.ai's tools, enterprises can enhance decision-making through AI-driven insights and predictive analytics, empowering informed strategies and agile responses to market changes.</p><h1>Implementing AI Workflows Successfully</h1><p>Successful implementation of AI agent workflows requires strategic planning and execution. Ruh.ai offers comprehensive support throughout the journey, ensuring businesses maximize the benefits of AI integration.</p><h2>Steps to Implementation</h2><p>The integration process begins with a detailed assessment of the current workflow infrastructure, followed by the design of tailored AI solutions and a phased rollout. Ongoing evaluation and optimization are critical to sustaining operational efficiency.</p><h2>Future Prospects</h2><p>The future of AI agent workflows promises continued innovation and adaptation, driven by advancements in machine learning and robotics. With companies like Ruh.ai at the helm, the potential for transformative results across industries is immense.</p>"
    },
    "timeout": null,
    "follow_redirects": null,
    "save_to_file": null,
    "output_format": null,
    "raise_on_error": null
  },
  "request_id": "d53e2469-d9cd-4fbc-8acb-91f9271eafc0",
  "correlation_id": "c2c33eb4-68b7-4953-99e5-ef05514dfea7"
}
2025-05-29 13:23:11 - ComponentSystem - INFO - [_process_message:713] [ReqID:d53e2469-d9cd-4fbc-8acb-91f9271eafc0] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Executing tool ApiRequestNode for RequestID=d53e2469-d9cd-4fbc-8acb-91f9271eafc0, TaskID=ApiRequestNode-node-execution-request-0-315-1748505191.8518567
2025-05-29 13:23:12 - ComponentSystem - INFO - [_process_message:717] [ReqID:d53e2469-d9cd-4fbc-8acb-91f9271eafc0] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Tool ApiRequestNode executed successfully for RequestID=d53e2469-d9cd-4fbc-8acb-91f9271eafc0, TaskID=ApiRequestNode-node-execution-request-0-315-1748505191.8518567
2025-05-29 13:23:12 - ComponentSystem - INFO - [_send_result:1005] [ReqID:d53e2469-d9cd-4fbc-8acb-91f9271eafc0] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Preparing to send result for component ApiRequestNode, RequestID=d53e2469-d9cd-4fbc-8acb-91f9271eafc0
2025-05-29 13:23:12 - ComponentSystem - INFO - [_send_result:1030] [ReqID:d53e2469-d9cd-4fbc-8acb-91f9271eafc0] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Result contains error status or error field for RequestID=d53e2469-d9cd-4fbc-8acb-91f9271eafc0
2025-05-29 13:23:12 - ComponentSystem - INFO - [_send_result:1118] [ReqID:d53e2469-d9cd-4fbc-8acb-91f9271eafc0] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Sending Kafka response: RequestID=d53e2469-d9cd-4fbc-8acb-91f9271eafc0, Response={
  "request_id": "d53e2469-d9cd-4fbc-8acb-91f9271eafc0",
  "status": "error",
  "result": {
    "component_type": "ApiRequestNode",
    "request_id": "d53e2469-d9cd-4fbc-8acb-91f9271eafc0",
    "response": {
      "result": {
        "detail": [
          {
            "type": "missing",
            "loc": [
              "body",
              "service_name"
            ],
            "msg": "Field required",
            "input": {
              "title": "Navigating AI Agent Workflows: Enhancing Operational Efficiency with Ruh.ai",
              "summary": "Discover how AI agent workflows revolutionize business processes by integrating adaptive learning and automation. Dive into the intricacies of workflow management and learn how Ruh.ai's solutions can effectively streamline operations, driving transformation across industries.",
              "category": "AI Innovation",
              "content": "<h1>The Evolution of AI Agent Workflows</h1><p>In the fast-paced world of technological advancement, AI agent workflows have emerged as a cornerstone for optimizing enterprise operations. These workflows synthesize automation, machine learning, and data analytics to deliver unprecedented efficiency.</p><h2>Understanding AI Agent Workflows</h2><p>AI agent workflows consist of a series of automated tasks executed by intelligent agents. These agents leverage cognitive computing and advanced algorithms to simulate human-like decision-making capabilities, epowering businesses to reduce manual intervention.</p><h3>Key Components:</h3><ul><li>Automation: Streamlining routine tasks for efficiency.</li><li>Machine Learning: Adapting algorithms for continuous improvement.</li><li>Data Analytics: Informing decisions through data-driven insights.</li></ul><h2>Transforming Operations with Ruh.ai</h2><p>Ruh.ai brings a bespoke approach to AI agent workflows, tailoring solutions to the unique requirements of different industries. By integrating cutting-edge AI technologies, Ruh.ai enables companies to achieve substantial operational transformation.</p><h3>Customized Solutions</h3><p>Ruh.ai develops customized AI workflows that align with specific business needs, ensuring seamless integration with existing systems and processes.</p><h3>Enhanced Decision-Making</h3><p>With Ruh.ai's tools, enterprises can enhance decision-making through AI-driven insights and predictive analytics, empowering informed strategies and agile responses to market changes.</p><h1>Implementing AI Workflows Successfully</h1><p>Successful implementation of AI agent workflows requires strategic planning and execution. Ruh.ai offers comprehensive support throughout the journey, ensuring businesses maximize the benefits of AI integration.</p><h2>Steps to Implementation</h2><p>The integration process begins with a detailed assessment of the current workflow infrastructure, followed by the design of tailored AI solutions and a phased rollout. Ongoing evaluation and optimization are critical to sustaining operational efficiency.</p><h2>Future Prospects</h2><p>The future of AI agent workflows promises continued innovation and adaptation, driven by advancements in machine learning and robotics. With companies like Ruh.ai at the helm, the potential for transformative results across industries is immense.</p>"
            }
          },
          {
            "type": "missing",
            "loc": [
              "body",
              "api_key"
            ],
            "msg": "Field required",
            "input": {
              "title": "Navigating AI Agent Workflows: Enhancing Operational Efficiency with Ruh.ai",
              "summary": "Discover how AI agent workflows revolutionize business processes by integrating adaptive learning and automation. Dive into the intricacies of workflow management and learn how Ruh.ai's solutions can effectively streamline operations, driving transformation across industries.",
              "category": "AI Innovation",
              "content": "<h1>The Evolution of AI Agent Workflows</h1><p>In the fast-paced world of technological advancement, AI agent workflows have emerged as a cornerstone for optimizing enterprise operations. These workflows synthesize automation, machine learning, and data analytics to deliver unprecedented efficiency.</p><h2>Understanding AI Agent Workflows</h2><p>AI agent workflows consist of a series of automated tasks executed by intelligent agents. These agents leverage cognitive computing and advanced algorithms to simulate human-like decision-making capabilities, epowering businesses to reduce manual intervention.</p><h3>Key Components:</h3><ul><li>Automation: Streamlining routine tasks for efficiency.</li><li>Machine Learning: Adapting algorithms for continuous improvement.</li><li>Data Analytics: Informing decisions through data-driven insights.</li></ul><h2>Transforming Operations with Ruh.ai</h2><p>Ruh.ai brings a bespoke approach to AI agent workflows, tailoring solutions to the unique requirements of different industries. By integrating cutting-edge AI technologies, Ruh.ai enables companies to achieve substantial operational transformation.</p><h3>Customized Solutions</h3><p>Ruh.ai develops customized AI workflows that align with specific business needs, ensuring seamless integration with existing systems and processes.</p><h3>Enhanced Decision-Making</h3><p>With Ruh.ai's tools, enterprises can enhance decision-making through AI-driven insights and predictive analytics, empowering informed strategies and agile responses to market changes.</p><h1>Implementing AI Workflows Successfully</h1><p>Successful implementation of AI agent workflows requires strategic planning and execution. Ruh.ai offers comprehensive support throughout the journey, ensuring businesses maximize the benefits of AI integration.</p><h2>Steps to Implementation</h2><p>The integration process begins with a detailed assessment of the current workflow infrastructure, followed by the design of tailored AI solutions and a phased rollout. Ongoing evaluation and optimization are critical to sustaining operational efficiency.</p><h2>Future Prospects</h2><p>The future of AI agent workflows promises continued innovation and adaptation, driven by advancements in machine learning and robotics. With companies like Ruh.ai at the helm, the potential for transformative results across industries is immense.</p>"
            }
          }
        ]
      },
      "status_code": 422,
      "response_headers": {},
      "error": "API request failed with status 422 (Unprocessable Entity)"
    }
  }
}
2025-05-29 13:23:12 - ComponentSystem - INFO - [_send_result:1127] [ReqID:d53e2469-d9cd-4fbc-8acb-91f9271eafc0] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Sent result for component ApiRequestNode to topic node_results for RequestID=d53e2469-d9cd-4fbc-8acb-91f9271eafc0
2025-05-29 13:23:13 - ComponentSystem - INFO - [_commit_offset:961] [ReqID:d53e2469-d9cd-4fbc-8acb-91f9271eafc0] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Successfully committed offset 316 for TopicPartition(topic='node-execution-request', partition=0) for component ApiRequestNode, TaskID=ApiRequestNode-node-execution-request-0-315-1748505191.8518567
2025-05-29 13:23:13 - ComponentSystem - INFO - [_process_message:936] [ReqID:d53e2469-d9cd-4fbc-8acb-91f9271eafc0] [CorrID:c2c33eb4-68b7-4953-99e5-ef05514dfea7] Finished message processing: Component=ApiRequestNode, Topic=node-execution-request, Partition=0, Offset=315, TaskID=ApiRequestNode-node-execution-request-0-315-1748505191.8518567
